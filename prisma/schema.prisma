// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserType {
  CONSUMER
  BUSINESS
}

enum BusinessType {
  RETAIL
  MERCHANT
}

enum Status {
  ACTIVE
  PENDING
  FREEZE
  DEACTIVE
}

enum KycStatus {
  PENDING
  APPROVED
  REJECTED
  DENIED
  CANCELED
  MANUAL_REVIEW
  LOCKED
}

enum KybStatus {
  PENDING
  APPROVED
  REJECTED
  DENIED
  CANCELED
  MANUAL_REVIEW
  LOCKED
}

enum IdentityType {
  KTP
  PASSPORT
  DRIVER_LICENSE
  SIM
}

enum NavStatus {
  WAITING_FOR_APPROVAL
  WAITING_FOR_POSTING
  POSTING_IN_PROCESS
  POSTED
  REJECTED
  POSTING_ERROR
}

model User {
  id               String        @id @default(uuid(7)) @db.Uuid
  email            String        @unique
  username         String        @unique
  password         String
  firstName        String        @map("first_name")
  lastName         String        @map("last_name")
  type             UserType
  businessType     BusinessType?
  status           Status        @default(PENDING)
  createdAt        DateTime      @default(now()) @map("created_at")
  updatedAt        DateTime      @updatedAt @map("updated_at")
  googleId         String?       @unique
  oauthProvider    String?
  oauthAccessToken String?

  kyc Kyc?

  kyb                Kyb?
  ActivationToken    ActivationToken[]
  PasswordResetToken PasswordResetToken[]
  refreshTokens      RefreshToken[]
  lastLoginAt        DateTime?            @map("last_login_at")
  tokenVersion       Int                  @default(0) @map("token_version")

  @@map("user")
}

model Kyc {
  id             String       @id @default(uuid(7)) @db.Uuid
  identityType   IdentityType @map("identity_type")
  identityNumber String       @map("identity_number")
  birthdate      DateTime
  birthplace     String
  address        String
  state          String
  country        String
  status         KycStatus    @default(PENDING)
  provider       String?
  zipNumber      String       @map("zip_number")
  phoneNumber    String       @map("phone_number")
  fileName       String?      @map("file_name")
  userId         String       @unique @map("user_id") @db.Uuid
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("kyc")
}

model Kyb {
  id                 String    @id @default(uuid(7)) @db.Uuid
  namaPerusahaan     String    @map("nama_perusahaan")
  address            String
  ubo                String
  contactInformation String    @map("contact_information")
  legalRegistration  String    @map("legal_registration")
  registrationNumber String    @map("registration_number")
  kybStatus          KybStatus @default(PENDING) @map("kyb_status")
  userId             String    @unique @map("user_id") @db.Uuid
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  provider           String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  uboProfiles UboProfile[]

  @@map("kyb")
}

model UboProfile {
  id              String       @id @default(uuid(7)) @db.Uuid
  identityType    IdentityType @map("identity_type")
  identityNumber  String       @map("identity_number")
  firstName       String       @map("first_name")
  lastName        String       @map("last_name")
  address         String
  telephoneNumber String       @map("telephone_number")
  kybId           String       @map("kyb_id") @db.Uuid
  createdAt       DateTime     @default(now()) @map("created_at")
  updatedAt       DateTime     @updatedAt @map("updated_at")

  kyb Kyb @relation(fields: [kybId], references: [id], onDelete: Cascade)

  @@map("ubo_profile")
}

model Role {
  id        String   @id @default(uuid(7)) @db.Uuid
  name      String   @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  admins Admin[]

  @@map("role")
}

model Admin {
  id        String   @id @default(uuid(7)) @db.Uuid
  email     String   @unique
  username  String   @unique
  password  String
  roleId    String   @map("role_id") @db.Uuid
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  role Role @relation(fields: [roleId], references: [id])

  logs Log[]

  @@map("admin")
}

model Log {
  id        String   @id @default(uuid(7)) @db.Uuid
  actions   String
  timestamp DateTime @default(now())
  message   String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  adminId   String?  @map("admin_id") @db.Uuid

  admin Admin? @relation(fields: [adminId], references: [id])

  @@map("log")
}

model ActivationToken {
  id        String   @id @default(uuid(7)) @db.Uuid
  token     String   @unique
  userId    String   @map("user_id") @db.Uuid
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("activation_token")
}

model PasswordResetToken {
  id        String   @id @default(uuid(7)) @db.Uuid
  token     String   @unique
  userId    String   @map("user_id") @db.Uuid
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_token")
}

model RefreshToken {
  id         String    @id @default(uuid(7)) @db.Uuid
  token      String    @unique
  userId     String    @map("user_id") @db.Uuid
  deviceId   String?   @map("device_id")
  userAgent  String?   @map("user_agent")
  ipAddress  String?   @map("ip_address")
  isRevoked  Boolean   @default(false) @map("is_revoked")
  expiresAt  DateTime  @map("expires_at")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  lastUsedAt DateTime? @map("last_used_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRevoked])
  @@index([token, expiresAt])
  @@map("refresh_token")
}

model AccessTokenBlacklist {
  id        String   @id @default(uuid(7)) @db.Uuid
  tokenHash String   @unique @map("token_hash")
  userId    String   @map("user_id") @db.Uuid
  expiresAt DateTime @map("expires_at")
  revokedAt DateTime @default(now()) @map("revoked_at")
  reason    String?

  @@index([tokenHash, expiresAt])
  @@map("access_token_blacklist")
}

model NavManagement {
  id          String    @id @default(uuid(7)) @db.Uuid
  date        DateTime
  price       Decimal   @db.Decimal(10, 2)
  status      NavStatus @default(WAITING_FOR_APPROVAL)
  notes       String?
  safeTxHash  String?   @map("safe_tx_hash")
  signature   String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@map("nav_management")
}
