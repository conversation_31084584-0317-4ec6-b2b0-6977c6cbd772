-- CreateEnum
CREATE TYPE "public"."NavStatus" AS ENUM ('WAITING_FOR_APPROVAL', 'WAITING_FOR_POSTING', 'POSTING_IN_PROCESS', 'POSTED', 'REJECTED', 'POSTING_ERROR');

-- CreateTable
CREATE TABLE "public"."nav_management" (
    "id" UUID NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "status" "public"."NavStatus" NOT NULL DEFAULT 'WAITING_FOR_APPROVAL',
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "nav_management_pkey" PRIMARY KEY ("id")
);
