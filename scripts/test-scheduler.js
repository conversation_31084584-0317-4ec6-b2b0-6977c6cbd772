#!/usr/bin/env node

/**
 * Test script to verify the scheduler system is working
 * This script simulates the scheduler functionality without running the actual cron job
 */

const { PrismaClient } = require('../generated/prisma');

async function testScheduler() {
  console.log('🧪 Testing Scheduler System...\n');

  const prisma = new PrismaClient();

  try {
    // Test 1: Check if we can connect to the database
    console.log('1️⃣ Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful\n');

    // Test 2: Check if advisory lock functions are available
    console.log('2️⃣ Testing PostgreSQL advisory lock functions...');
    const lockResult = await prisma.$queryRaw`
      SELECT pg_try_advisory_lock(12345) as lock_acquired
    `;
    
    if (lockResult[0]?.lock_acquired) {
      console.log('✅ Advisory lock acquired successfully');
      
      // Release the lock
      await prisma.$queryRaw`
        SELECT pg_advisory_unlock(12345)
      `;
      console.log('✅ Advisory lock released successfully\n');
    } else {
      console.log('❌ Failed to acquire advisory lock\n');
    }

    // Test 3: Check NavManagement table structure
    console.log('3️⃣ Testing NavManagement table...');
    const navCount = await prisma.navManagement.count();
    console.log(`✅ NavManagement table accessible, found ${navCount} records\n`);

    // Test 4: Check for WAITING_FOR_APPROVAL transactions
    console.log('4️⃣ Checking for transactions waiting for approval...');
    const waitingTransactions = await prisma.navManagement.findMany({
      where: {
        status: 'WAITING_FOR_APPROVAL',
        safeTxHash: {
          not: null,
        },
      },
      select: {
        id: true,
        safeTxHash: true,
        status: true,
        updatedAt: true,
      },
    });

    console.log(`✅ Found ${waitingTransactions.length} transactions waiting for approval`);
    
    if (waitingTransactions.length > 0) {
      console.log('📋 Sample transactions:');
      waitingTransactions.slice(0, 3).forEach((tx, index) => {
        console.log(`   ${index + 1}. ID: ${tx.id}, Hash: ${tx.safeTxHash?.substring(0, 20)}...`);
      });
    }
    console.log('');

    // Test 5: Check environment variables
    console.log('5️⃣ Checking required environment variables...');
    const requiredEnvVars = [
      'DATABASE_URL',
      'SAFE_API_KEY',
      'SAFE_ADDRESS',
      'SAFE_SENDER_ADDRESS',
      'SAFE_SENDER_PRIVATE_KEY',
      'NAV_MANAGER_CONTRACT_ADDRESS',
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length === 0) {
      console.log('✅ All required environment variables are set');
    } else {
      console.log('⚠️ Missing environment variables:');
      missingVars.forEach(varName => {
        console.log(`   - ${varName}`);
      });
    }
    console.log('');

    console.log('🎉 Scheduler system test completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Set APP_ROLE=jobs to run the scheduler');
    console.log('   2. Use "yarn dev:jobs" to start the scheduler in development');
    console.log('   3. Use "yarn start:jobs" to start the scheduler in production');
    console.log('   4. Monitor logs for scheduler activity');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Ensure PostgreSQL is running and accessible');
    console.error('   2. Check DATABASE_URL environment variable');
    console.error('   3. Run "yarn prisma:generate" to generate Prisma client');
    console.error('   4. Run "yarn prisma:migrate" to apply database migrations');
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testScheduler().catch(console.error);
