# fly.toml app configuration file generated for orbitum-app on 2025-09-01T14:02:08+07:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'orbitum-app'
primary_region = 'sin'

[build]

[deploy]
  release_command = "npx prisma migrate deploy"

[env]
  NODE_ENV = 'production'

[processes]
  web    = "node dist/main.api.js"
  worker = "node dist/main.jobs.js"

[[services]]
  processes = ["web"]
  internal_port = 3000
  protocol = "tcp"

  auto_start_machines = true
  auto_stop_machines  = true
  min_machines_running = 0

  [[services.ports]]
    port = 80
    handlers = ["http"]

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [[services.http_checks]]
    interval     = "30s"
    timeout      = "5s"
    grace_period = "10s"
    method       = "GET"
    path         = "/api/v1/health"


[[vm]]
  processes = ["web"]
  cpu_kind = "shared"
  cpus = 1
  memory = "512mb"

[[vm]]
  processes = ["worker"]
  cpu_kind = "shared"
  cpus = 1
  memory = "128mb" 
