import { Logger } from '@nestjs/common';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  // Determine application role
  const appRole = process.env.APP_ROLE || 'api';
  logger.log(`🚀 Starting application with role: ${appRole}`);

  if (appRole === 'jobs') {
    // Start Jobs/Worker service
    logger.log('📦 Loading Jobs/Worker service...');
    const { startJobsService } = await import('./main.jobs');
    await startJobsService();
  } else {
    // Start API service (default)
    logger.log('🌐 Loading API service...');
    const { startApiService } = await import('./main.api');
    await startApiService();
  }
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start application:', error);
  process.exit(1);
});
