import { NestFactory } from '@nestjs/core';
import { JobsModule } from './jobs/jobs.module';
import { Logger } from '@nestjs/common';

export async function startJobsService() {
  // Guard: Only run if APP_ROLE is set to 'jobs'
  if (process.env.APP_ROLE !== 'jobs') {
    console.log('❌ APP_ROLE is not set to "jobs". Exiting...');
    process.exit(0);
  }

  const logger = new Logger('JobsBootstrap');

  try {
    const app = await NestFactory.createApplicationContext(JobsModule, {
      logger:
        process.env.NODE_ENV === 'production'
          ? ['error', 'warn', 'log']
          : ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Enable graceful shutdown hooks
    app.enableShutdownHooks();

    logger.log('🚀 Jobs/Worker service started successfully');
    logger.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.log('⏰ Scheduler jobs are now running...');

    // Setup graceful shutdown handlers
    setupGracefulShutdown(app, logger);

    // Keep the application running
    await new Promise(() => {});
  } catch (error) {
    logger.error('❌ Failed to start Jobs/Worker service:', error);
    process.exit(1);
  }
}

// Graceful shutdown function
function setupGracefulShutdown(app: any, logger: Logger) {
  const gracefulShutdown = async (signal: string) => {
    logger.log(
      `🛑 ${signal} received, shutting down Jobs/Worker service gracefully...`,
    );

    try {
      // Close the NestJS application context
      await app.close();
      logger.log('✅ Jobs/Worker service closed successfully');

      // Exit the process
      process.exit(0);
    } catch (error) {
      logger.error('❌ Error during Jobs/Worker service shutdown:', error);
      process.exit(1);
    }
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  // Handle uncaught exceptions
  process.on('uncaughtException', async (error) => {
    logger.error('❌ Uncaught Exception in Jobs/Worker service:', error);
    gracefulShutdown('UNCAUGHT_EXCEPTION');
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', async (reason, promise) => {
    logger.error(
      '❌ Unhandled Rejection in Jobs/Worker service at:',
      promise,
      'reason:',
      reason,
    );
    gracefulShutdown('UNHANDLED_REJECTION');
  });

  logger.log(
    '🔧 Graceful shutdown handlers registered for Jobs/Worker service',
  );
}

// Only run if this file is executed directly
if (require.main === module) {
  startJobsService().catch((error) => {
    console.error('❌ Failed to start Jobs/Worker service:', error);
    process.exit(1);
  });
}
