import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { PrismaService } from '../../prisma/prisma.service';
import { SafeService } from '../../backoffice/nav-management/safe.service';
import { NavStatus } from '../../../generated/prisma';

@Injectable()
export class TransactionStatusSchedulerService {
  private readonly logger = new Logger(TransactionStatusSchedulerService.name);
  private readonly JOB_LOCK_ID = 12345; // Unique lock ID for this job

  constructor(
    private readonly prisma: PrismaService,
    private readonly safeService: SafeService,
  ) {}

  /**
   * Cron job that runs every 30 seconds to check transaction statuses
   * Uses PostgreSQL advisory lock to prevent race conditions
   */
  @Cron('*/30 * * * * *', {
    name: 'check-transaction-status',
    timeZone: 'UTC',
  })
  async checkTransactionStatuses(): Promise<void> {
    const startTime = Date.now();
    this.logger.log('🔄 Starting transaction status check job...');

    try {
      // Try to acquire advisory lock
      const lockAcquired = await this.acquireAdvisoryLock();

      if (!lockAcquired) {
        this.logger.log(
          '⏭️ Another worker is already processing transactions, skipping this run',
        );
        return;
      }

      this.logger.log('🔒 Advisory lock acquired, processing transactions...');

      // Get all transactions with WAITING_FOR_APPROVAL status
      const waitingTransactions = await this.getWaitingTransactions();

      if (waitingTransactions.length === 0) {
        this.logger.log('✅ No transactions waiting for approval found');
        return;
      }

      this.logger.log(
        `📋 Found ${waitingTransactions.length} transactions to check`,
      );

      // Process each transaction
      let processedCount = 0;
      let successCount = 0;
      let errorCount = 0;

      for (const transaction of waitingTransactions) {
        try {
          await this.processTransaction(transaction);
          successCount++;
        } catch (error) {
          this.logger.error(
            `❌ Error processing transaction ${transaction.id}:`,
            error.message,
          );
          errorCount++;
        }
        processedCount++;
      }

      const duration = Date.now() - startTime;
      this.logger.log(
        `✅ Transaction status check completed in ${duration}ms. ` +
          `Processed: ${processedCount}, Success: ${successCount}, Errors: ${errorCount}`,
      );
    } catch (error) {
      this.logger.error('❌ Error in transaction status check job:', error);
    } finally {
      // Always release the advisory lock
      await this.releaseAdvisoryLock();
      this.logger.log('🔓 Advisory lock released');
    }
  }

  /**
   * Acquire PostgreSQL advisory lock to prevent concurrent execution
   */
  private async acquireAdvisoryLock(): Promise<boolean> {
    try {
      const result = await this.prisma.$queryRaw<
        [{ pg_try_advisory_lock: boolean }]
      >`
        SELECT pg_try_advisory_lock(${this.JOB_LOCK_ID}) as pg_try_advisory_lock
      `;

      return result[0]?.pg_try_advisory_lock || false;
    } catch (error) {
      this.logger.error('❌ Failed to acquire advisory lock:', error);
      return false;
    }
  }

  /**
   * Release PostgreSQL advisory lock
   */
  private async releaseAdvisoryLock(): Promise<void> {
    try {
      await this.prisma.$queryRaw`
        SELECT pg_advisory_unlock(${this.JOB_LOCK_ID})
      `;
    } catch (error) {
      this.logger.error('❌ Failed to release advisory lock:', error);
    }
  }

  /**
   * Get all transactions with WAITING_FOR_APPROVAL status
   */
  private async getWaitingTransactions() {
    return await this.prisma.navManagement.findMany({
      where: {
        status: NavStatus.WAITING_FOR_APPROVAL,
        safeTxHash: {
          not: null,
        },
      },
      select: {
        id: true,
        safeTxHash: true,
        status: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: 'asc', // Process oldest first
      },
    });
  }

  /**
   * Process a single transaction by checking its status
   */
  private async processTransaction(transaction: {
    id: string;
    safeTxHash: string;
    status: NavStatus;
    updatedAt: Date;
  }): Promise<void> {
    this.logger.debug(
      `🔍 Checking transaction ${transaction.id} with hash ${transaction.safeTxHash}`,
    );

    try {
      // Get transaction status from SAFE
      const safeTransaction = await this.safeService.getTransaction(
        transaction.safeTxHash,
      );

      if (!safeTransaction) {
        this.logger.warn(`⚠️ Transaction ${transaction.id} not found in SAFE`);
        await this.updateTransactionStatus(
          transaction.id,
          NavStatus.POSTING_ERROR,
          'Transaction not found in SAFE',
        );
        return;
      }

      // Determine new status based on SAFE transaction status
      const newStatus = this.determineTransactionStatus(safeTransaction);

      if (newStatus !== transaction.status) {
         await this.updateTransactionStatus(
           transaction.id,
           newStatus,
           `Status updated from SAFE: ${this.getTransactionStatus(safeTransaction)}`,
         );

        this.logger.log(
          `✅ Transaction ${transaction.id} status updated: ${transaction.status} → ${newStatus}`,
        );
      } else {
        this.logger.debug(
          `ℹ️ Transaction ${transaction.id} status unchanged: ${transaction.status}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `❌ Error checking transaction ${transaction.id}:`,
        error.message,
      );

      // Update status to POSTING_ERROR if there's an error
      await this.updateTransactionStatus(
        transaction.id,
        NavStatus.POSTING_ERROR,
        `Error checking transaction: ${error.message}`,
      );
    }
  }

  /**
   * Determine the new transaction status based on SAFE transaction data
   */
   private getTransactionStatus(safeTransaction: any): string {
     if (safeTransaction.isExecuted) {
       if (safeTransaction.isSuccessful === true) {
         return 'EXECUTED_SUCCESSFULLY';
       } else if (safeTransaction.isSuccessful === false) {
         return 'EXECUTED_FAILED';
       } else {
         return 'EXECUTED_UNKNOWN_SUCCESS';
       }
     } else {
       return 'NOT_EXECUTED';
     }
   }

   private determineTransactionStatus(safeTransaction: any): NavStatus {
     if (safeTransaction.isExecuted) {
       if (safeTransaction.isSuccessful === true) {
         // Transaction is executed and successful
         return NavStatus.POSTED;
       } else {
         // Transaction is executed but not successful (or isSuccessful is null/false)
         return NavStatus.POSTING_ERROR;
       }
     } else {
       // Transaction is not executed yet, keep waiting
       return NavStatus.WAITING_FOR_APPROVAL;
     }
   }

  /**
   * Update transaction status in database
   */
  private async updateTransactionStatus(
    transactionId: string,
    newStatus: NavStatus,
    notes?: string,
  ): Promise<void> {
    try {
      await this.prisma.navManagement.update({
        where: { id: transactionId },
        data: {
          status: newStatus,
          notes: notes
            ? `${notes} (Updated at ${new Date().toISOString()})`
            : undefined,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(
        `❌ Failed to update transaction ${transactionId} status:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Manual trigger for testing purposes
   */
  async triggerManualCheck(): Promise<{ message: string; processed: number }> {
    this.logger.log('🔧 Manual transaction status check triggered');

    const startTime = Date.now();
    const waitingTransactions = await this.getWaitingTransactions();

    if (waitingTransactions.length === 0) {
      return { message: 'No transactions waiting for approval', processed: 0 };
    }

    let processedCount = 0;
    for (const transaction of waitingTransactions) {
      try {
        await this.processTransaction(transaction);
        processedCount++;
      } catch (error) {
        this.logger.error(
          `Error processing transaction ${transaction.id}:`,
          error,
        );
      }
    }

    const duration = Date.now() - startTime;
    return {
      message: `Manual check completed in ${duration}ms`,
      processed: processedCount,
    };
  }
}
