import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { PrismaModule } from '../prisma/prisma.module';
import { NavManagementModule } from '../backoffice/nav-management/nav-management.module';
import { TransactionStatusSchedulerService } from './services/transaction-status-scheduler.service';

@Module({
  imports: [ScheduleModule.forRoot(), PrismaModule, NavManagementModule],
  providers: [TransactionStatusSchedulerService],
  exports: [TransactionStatusSchedulerService],
})
export class JobsModule {}
