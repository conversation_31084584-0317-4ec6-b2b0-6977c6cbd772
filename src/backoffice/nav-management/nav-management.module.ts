import { Module } from '@nestjs/common';
import { NavManagementController } from './nav-management.controller';
import { NavManagementService } from './nav-management.service';
import { SafeService } from './safe.service';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [NavManagementController],
  providers: [NavManagementService, SafeService],
  exports: [NavManagementService, SafeService],
})
export class NavManagementModule {}
