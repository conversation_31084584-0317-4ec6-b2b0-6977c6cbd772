import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateNavManagementDto } from './dto/create-nav-management.dto';
import { NavManagementResponseDto } from './dto/nav-management-response.dto';
import { NavManagementQueryDto } from './dto/nav-management-query.dto';
import {
  NavManagementListResponseDto,
  PaginationMetaDto,
} from './dto/nav-management-list-response.dto';
import { NavManagementListApiResponseDto } from './dto/nav-management-api-response.dto';
import { SafeService } from './safe.service';
import { BaseResponseDto } from './dto/base-response.dto';

@Injectable()
export class NavManagementService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly safeService: SafeService,
  ) {}

  async create(
    createNavManagementDto: CreateNavManagementDto,
  ): Promise<BaseResponseDto<NavManagementResponseDto>> {
    try {
      const { date, price } = createNavManagementDto;

      // Convert YYYY-MM-DD string to Date object
      const navDate = new Date(date + 'T00:00:00.000Z');

      // Validate that the date is valid
      if (isNaN(navDate.getTime())) {
        throw new BadRequestException('Invalid date format');
      }

      // Check if a NAV entry already exists for this date (excluding rejected entries)
      const existingNav = await this.prisma.navManagement.findFirst({
        where: {
          date: navDate,
          status: {
            not: 'REJECTED',
          },
        },
      });

      if (existingNav) {
        throw new BadRequestException('NAV entry already exists for this date');
      }

      // Get SAFE configuration from environment variables
      const safeAddress = process.env.SAFE_ADDRESS;
      const senderAddress = process.env.SAFE_SENDER_ADDRESS;
      const senderPrivateKey = process.env.SAFE_SENDER_PRIVATE_KEY;
      const navManagerContractAddress =
        process.env.NAV_MANAGER_CONTRACT_ADDRESS;
      const rpcUrl = process.env.RPC_URL || 'https://arb1.arbitrum.io/rpc';

      if (
        !safeAddress ||
        !senderAddress ||
        !senderPrivateKey ||
        !navManagerContractAddress
      ) {
        throw new InternalServerErrorException(
          'SAFE or NAV Manager contract configuration is missing',
        );
      }

      const encodedData = this.safeService.encodeAddPriceFunction(price);
      // Propose transaction to SAFE to call addPrice function
      const safeResult = await this.safeService.proposeTransaction(
        safeAddress,
        navManagerContractAddress, // NAV Manager contract address
        '0', // No ETH value for contract call
        encodedData, // Encoded function call data
        senderAddress,
        senderPrivateKey,
        rpcUrl,
      );

      // Only create the NAV entry if SAFE proposal was successful
      const navEntry = await this.prisma.navManagement.create({
        data: {
          date: navDate,
          price,
          status: 'WAITING_FOR_APPROVAL', // Set status to WAITING_FOR_APPROVAL
          notes: `SAFE transaction proposed successfully to call addPrice(${price}) on NAV Manager contract. TxHash: ${safeResult.safeTxHash}`,
          safeTxHash: safeResult.safeTxHash,
          signature: safeResult.signature,
        },
      });

      const navResponse = new NavManagementResponseDto({
        id: navEntry.id,
        date: navEntry.date,
        price: Number(navEntry.price),
        status: navEntry.status as any,
        notes: navEntry.notes,
        createdAt: navEntry.createdAt,
        updatedAt: navEntry.updatedAt,
        safeTxHash: navEntry.safeTxHash,
        signature: navEntry.signature,
      });

      return BaseResponseDto.success(
        navResponse,
        'NAV entry created and SAFE transaction proposed to call addPrice function on NAV Manager contract',
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      console.error('NavManagementService Error:', error);

      // If it's a SAFE-related error, provide more specific error message
      if (
        error.message?.includes('SAFE') ||
        error.message?.includes('Failed to propose transaction')
      ) {
        throw new InternalServerErrorException(
          `Failed to propose transaction to SAFE: ${error.message}`,
        );
      }

      throw new InternalServerErrorException(
        'An error occurred while creating the NAV entry',
      );
    }
  }

  async findAll(
    query: NavManagementQueryDto,
  ): Promise<NavManagementListApiResponseDto> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'date',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;

      // Get total count
      const total = await this.prisma.navManagement.count();

      // Get paginated data
      const navEntries = await this.prisma.navManagement.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: {
          id: true,
          date: true,
          price: true,
          status: true,
          notes: true,
          safeTxHash: true,
          signature: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta: PaginationMetaDto = {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      };

      // Transform data to response DTOs
      const data = navEntries.map(
        (entry) =>
          new NavManagementResponseDto({
            id: entry.id,
            date: entry.date,
            price: Number(entry.price),
            status: entry.status as any,
            notes: entry.notes,
            safeTxHash: entry.safeTxHash,
            signature: entry.signature,
            createdAt: entry.createdAt,
            updatedAt: entry.updatedAt,
          }),
      );

      const listResponse = new NavManagementListResponseDto({
        data,
        meta,
      });

      return new NavManagementListApiResponseDto(
        listResponse,
        'NAV entries retrieved successfully',
      );
    } catch (error) {
      console.error('NavManagementService findAll Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching NAV entries',
      );
    }
  }
}
