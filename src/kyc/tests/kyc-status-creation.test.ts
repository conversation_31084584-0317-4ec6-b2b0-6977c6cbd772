import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { KycService } from '../kyc.service';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateKycDto, KycStatus, IdentityType } from '../dto/create-kyc.dto';

describe('KycService - Status Creation', () => {
  let service: KycService;
  let prismaService: PrismaService;

  const mockUser = {
    id: 'user-123',
    status: 'ACTIVE',
    email: '<EMAIL>',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedpassword',
    type: 'INDIVIDUAL',
    businessType: null,
    googleId: null,
    oauthProvider: null,
    oauthAccessToken: null,
    tokenVersion: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as any;

  const mockKycData: CreateKycDto = {
    identityType: IdentityType.KTP,
    identityNumber: '1234567890123456',
    birthdate: '1990-01-15',
    birthplace: 'Jakarta',
    address: 'Jl. Test No. 123, Jakarta',
    state: 'DKI Jakarta',
    country: 'Indonesia',
    zipNumber: '12345',
    phoneNumber: '+6281234567890',
    fileName: 'ktp_scan.jpg',
    provider: 'MANUAL',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KycService,
        {
          provide: PrismaService,
          useValue: {
            user: {
              findUnique: jest.fn(),
            },
            kyc: {
              findUnique: jest.fn(),
              create: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<KycService>(KycService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  describe('Status Determination', () => {
    beforeEach(() => {
      jest.spyOn(prismaService.user, 'findUnique').mockResolvedValue(mockUser);
      jest.spyOn(prismaService.kyc, 'findUnique').mockResolvedValue(null);
    });

    it('should default to PENDING when no status is provided', async () => {
      const mockCreatedKyc = {
        id: 'kyc-123',
        ...mockKycData,
        birthdate: new Date(mockKycData.birthdate),
        status: KycStatus.PENDING,
        provider: 'MANUAL',
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any;

      jest.spyOn(prismaService.kyc, 'create').mockResolvedValue(mockCreatedKyc);

      const result = await service.create(mockKycData, 'user-123');

      expect(result.status).toBe(KycStatus.PENDING);
      expect(prismaService.kyc.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: KycStatus.PENDING,
        }),
      });
    });

    it('should accept PENDING status when explicitly provided', async () => {
      const kycDataWithStatus = {
        ...mockKycData,
        status: KycStatus.PENDING,
      };

      const mockCreatedKyc = {
        id: 'kyc-123',
        ...kycDataWithStatus,
        birthdate: new Date(kycDataWithStatus.birthdate),
        provider: 'MANUAL',
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any;

      jest.spyOn(prismaService.kyc, 'create').mockResolvedValue(mockCreatedKyc);

      const result = await service.create(kycDataWithStatus, 'user-123');

      expect(result.status).toBe(KycStatus.PENDING);
    });

    it('should accept APPROVED status when provided (admin scenario)', async () => {
      const kycDataWithStatus = {
        ...mockKycData,
        status: KycStatus.APPROVED,
      };

      const mockCreatedKyc = {
        id: 'kyc-123',
        ...kycDataWithStatus,
        birthdate: new Date(kycDataWithStatus.birthdate),
        provider: 'MANUAL',
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any;

      jest.spyOn(prismaService.kyc, 'create').mockResolvedValue(mockCreatedKyc);

      const result = await service.create(kycDataWithStatus, 'user-123');

      expect(result.status).toBe(KycStatus.APPROVED);
    });

    it('should default to PENDING for disallowed statuses', async () => {
      const kycDataWithStatus = {
        ...mockKycData,
        status: KycStatus.LOCKED, // Not allowed for creation
      };

      const mockCreatedKyc = {
        id: 'kyc-123',
        ...mockKycData,
        birthdate: new Date(mockKycData.birthdate),
        status: KycStatus.PENDING, // Should be defaulted
        provider: 'MANUAL',
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any;

      jest.spyOn(prismaService.kyc, 'create').mockResolvedValue(mockCreatedKyc);

      const result = await service.create(kycDataWithStatus, 'user-123');

      expect(result.status).toBe(KycStatus.PENDING);
      expect(prismaService.kyc.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: KycStatus.PENDING,
        }),
      });
    });

    it('should handle invalid status gracefully', async () => {
      const kycDataWithInvalidStatus = {
        ...mockKycData,
        status: 'INVALID_STATUS' as KycStatus,
      };

      const mockCreatedKyc = {
        id: 'kyc-123',
        ...mockKycData,
        birthdate: new Date(mockKycData.birthdate),
        status: KycStatus.PENDING,
        provider: 'MANUAL',
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any;

      jest.spyOn(prismaService.kyc, 'create').mockResolvedValue(mockCreatedKyc);

      const result = await service.create(kycDataWithInvalidStatus, 'user-123');

      expect(result.status).toBe(KycStatus.PENDING);
    });
  });

  describe('Provider Handling', () => {
    beforeEach(() => {
      jest.spyOn(prismaService.user, 'findUnique').mockResolvedValue(mockUser);
      jest.spyOn(prismaService.kyc, 'findUnique').mockResolvedValue(null);
    });

    it('should default to MANUAL when no provider is specified', async () => {
      const kycDataWithoutProvider = {
        ...mockKycData,
        provider: undefined,
      };

      const mockCreatedKyc = {
        id: 'kyc-123',
        ...kycDataWithoutProvider,
        birthdate: new Date(kycDataWithoutProvider.birthdate),
        provider: 'MANUAL',
        status: KycStatus.PENDING,
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any;

      jest.spyOn(prismaService.kyc, 'create').mockResolvedValue(mockCreatedKyc);

      const result = await service.create(kycDataWithoutProvider, 'user-123');

      expect(result.provider).toBe('MANUAL');
      expect(prismaService.kyc.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          provider: 'MANUAL',
        }),
      });
    });

    it('should accept SUMSUB provider when specified', async () => {
      const kycDataWithSumsub = {
        ...mockKycData,
        provider: 'SUMSUB',
      };

      const mockCreatedKyc = {
        id: 'kyc-123',
        ...kycDataWithSumsub,
        birthdate: new Date(kycDataWithSumsub.birthdate),
        status: KycStatus.PENDING,
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any;

      jest.spyOn(prismaService.kyc, 'create').mockResolvedValue(mockCreatedKyc);

      const result = await service.create(kycDataWithSumsub, 'user-123');

      expect(result.provider).toBe('SUMSUB');
    });
  });

  describe('Logging and Audit', () => {
    beforeEach(() => {
      jest.spyOn(prismaService.user, 'findUnique').mockResolvedValue(mockUser);
      jest.spyOn(prismaService.kyc, 'findUnique').mockResolvedValue(null);
    });

    it('should log KYC creation operation', async () => {
      const mockCreatedKyc = {
        id: 'kyc-123',
        ...mockKycData,
        birthdate: new Date(mockKycData.birthdate),
        status: KycStatus.PENDING,
        provider: 'MANUAL',
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any;

      jest.spyOn(prismaService.kyc, 'create').mockResolvedValue(mockCreatedKyc);
      const logSpy = jest.spyOn(service['logger'], 'log');

      await service.create(mockKycData, 'user-123');

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('KYC Operation'),
      );
      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('CREATE'),
      );
    });
  });
});
