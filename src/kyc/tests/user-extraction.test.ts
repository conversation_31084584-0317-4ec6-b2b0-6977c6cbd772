import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { UserExtractionUtil } from '../utils/user-extraction.util';
import { AuthenticatedUser } from '../interfaces/authenticated-user.interface';

describe('UserExtractionUtil', () => {
  const mockAuthenticatedUser: AuthenticatedUser = {
    id: 'user-123',
    email: '<EMAIL>',
    username: 'testuser',
    type: 'INDIVIDUAL',
    status: 'ACTIVE',
    tokenVersion: 1,
  };

  describe('extractUserId', () => {
    it('should extract user ID from valid authenticated request', () => {
      const mockRequest = {
        user: mockAuthenticatedUser,
      };

      const userId = UserExtractionUtil.extractUserId(mockRequest);
      expect(userId).toBe('user-123');
    });

    it('should throw BadRequestException when request is null', () => {
      expect(() => {
        UserExtractionUtil.extractUserId(null);
      }).toThrow(BadRequestException);
    });

    it('should throw UnauthorizedException when user is not in request', () => {
      const mockRequest = {};

      expect(() => {
        UserExtractionUtil.extractUserId(mockRequest);
      }).toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException when user object is invalid', () => {
      const mockRequest = {
        user: {
          id: '', // Invalid empty ID
          email: '<EMAIL>',
        },
      };

      expect(() => {
        UserExtractionUtil.extractUserId(mockRequest);
      }).toThrow(UnauthorizedException);
    });

    it('should throw BadRequestException when user ID is empty string', () => {
      const mockRequest = {
        user: {
          ...mockAuthenticatedUser,
          id: '   ', // Whitespace only
        },
      };

      expect(() => {
        UserExtractionUtil.extractUserId(mockRequest);
      }).toThrow(BadRequestException);
    });

    it('should trim whitespace from user ID', () => {
      const mockRequest = {
        user: {
          ...mockAuthenticatedUser,
          id: '  user-123  ',
        },
      };

      const userId = UserExtractionUtil.extractUserId(mockRequest);
      expect(userId).toBe('user-123');
    });
  });

  describe('extractUser', () => {
    it('should extract full user object from valid request', () => {
      const mockRequest = {
        user: mockAuthenticatedUser,
      };

      const user = UserExtractionUtil.extractUser(mockRequest);
      expect(user).toEqual(mockAuthenticatedUser);
    });
  });

  describe('isUserAuthenticated', () => {
    it('should return true for valid authenticated request', () => {
      const mockRequest = {
        user: mockAuthenticatedUser,
      };

      const isAuthenticated = UserExtractionUtil.isUserAuthenticated(mockRequest);
      expect(isAuthenticated).toBe(true);
    });

    it('should return false for request without user', () => {
      const mockRequest = {};

      const isAuthenticated = UserExtractionUtil.isUserAuthenticated(mockRequest);
      expect(isAuthenticated).toBe(false);
    });
  });

  describe('extractUserEmail', () => {
    it('should extract user email from valid request', () => {
      const mockRequest = {
        user: mockAuthenticatedUser,
      };

      const email = UserExtractionUtil.extractUserEmail(mockRequest);
      expect(email).toBe('<EMAIL>');
    });
  });

  describe('isUserActive', () => {
    it('should return true for active user', () => {
      const mockRequest = {
        user: mockAuthenticatedUser,
      };

      const isActive = UserExtractionUtil.isUserActive(mockRequest);
      expect(isActive).toBe(true);
    });

    it('should return false for inactive user', () => {
      const mockRequest = {
        user: {
          ...mockAuthenticatedUser,
          status: 'PENDING',
        },
      };

      const isActive = UserExtractionUtil.isUserActive(mockRequest);
      expect(isActive).toBe(false);
    });
  });
});
