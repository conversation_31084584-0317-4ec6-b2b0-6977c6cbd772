import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { AuthenticatedUser, isAuthenticatedUser } from '../interfaces/authenticated-user.interface';

/**
 * Utility class for extracting and validating user information from requests
 * Follows Single Responsibility Principle and provides reusable user extraction logic
 */
export class UserExtractionUtil {
  /**
   * Safely extract user ID from authenticated request
   * @param req - Express request object with user attached by JWT guard
   * @returns User ID string
   * @throws UnauthorizedException if user is not authenticated
   * @throws BadRequestException if user ID is invalid
   */
  static extractUserId(req: any): string {
    // Validate request object
    if (!req) {
      throw new BadRequestException('Request object is required');
    }

    // Check if user exists in request (set by JWT guard)
    if (!req.user) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Validate user object structure using type guard
    if (!isAuthenticatedUser(req.user)) {
      throw new UnauthorizedException('Invalid user authentication data');
    }

    const userId = req.user.id;

    // Additional validation for user ID
    if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
      throw new BadRequestException('Invalid user ID');
    }

    return userId.trim();
  }

  /**
   * Extract full authenticated user object from request
   * @param req - Express request object with user attached by JWT guard
   * @returns AuthenticatedUser object
   * @throws UnauthorizedException if user is not authenticated
   */
  static extractUser(req: any): AuthenticatedUser {
    // Validate request object
    if (!req) {
      throw new BadRequestException('Request object is required');
    }

    // Check if user exists in request
    if (!req.user) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Validate user object structure
    if (!isAuthenticatedUser(req.user)) {
      throw new UnauthorizedException('Invalid user authentication data');
    }

    return req.user;
  }

  /**
   * Check if request has valid authenticated user
   * @param req - Express request object
   * @returns boolean indicating if user is properly authenticated
   */
  static isUserAuthenticated(req: any): boolean {
    return req && req.user && isAuthenticatedUser(req.user);
  }

  /**
   * Extract user email from authenticated request
   * @param req - Express request object with user attached by JWT guard
   * @returns User email string
   * @throws UnauthorizedException if user is not authenticated
   */
  static extractUserEmail(req: any): string {
    const user = this.extractUser(req);
    return user.email;
  }

  /**
   * Extract user status from authenticated request
   * @param req - Express request object with user attached by JWT guard
   * @returns User status string
   * @throws UnauthorizedException if user is not authenticated
   */
  static extractUserStatus(req: any): string {
    const user = this.extractUser(req);
    return user.status;
  }

  /**
   * Check if authenticated user has active status
   * @param req - Express request object with user attached by JWT guard
   * @returns boolean indicating if user is active
   * @throws UnauthorizedException if user is not authenticated
   */
  static isUserActive(req: any): boolean {
    const user = this.extractUser(req);
    return user.status === 'ACTIVE';
  }
}
