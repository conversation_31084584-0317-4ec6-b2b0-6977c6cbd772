import {
  Injectable,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateKycDto, IdentityType, KycStatus } from './dto/create-kyc.dto';
import { KycResponseDto, KycStatusResponseDto } from './dto/kyc-response.dto';
import { UserEntity } from './interfaces/kyc.interface';
import {
  VALID_USER_STATUSES,
  STATUS_MESSAGES,
  ERROR_MESSAGES,
} from './constants/kyc.constants';
import { StatusValidatorUtil } from './utils/status-validator.util';

/**
 * KYC Service with clean code patterns and comprehensive business logic
 * Follows SOLID principles and provides robust KYC management functionality
 */
@Injectable()
export class KycService {
  private readonly logger = new Logger(KycService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Initiates KYC process for user with enhanced provider integration
   * Uses transactions for data consistency and improved error handling
   */
  async initiateKyc(
    createKycDto: CreateKycDto,
    userId: string,
  ): Promise<KycResponseDto> {
    try {
      // Validasi user exists dan aktif
      const user = await this.validateUserExists(userId);

      // Cek apakah user sudah memiliki KYC
      await this.checkExistingKyc(userId);

      // Validasi data KYC
      this.validateKycData(createKycDto);

      // Determine provider - default to MANUAL if not specified
      const provider = createKycDto.provider?.trim() || 'MANUAL';

      // Determine status - validate and set appropriate status using the utility
      const status = StatusValidatorUtil.determineInitialStatus(
        createKycDto.status,
      );

      // Use transaction to ensure data consistency
      return await this.prisma.$transaction(async (prisma) => {
        // Create KYC data with enhanced provider handling
        const kyc = await prisma.kyc.create({
          data: {
            identityType: createKycDto.identityType,
            identityNumber: createKycDto.identityNumber.trim(),
            birthdate: new Date(createKycDto.birthdate),
            birthplace: createKycDto.birthplace.trim(),
            address: createKycDto.address.trim(),
            state: createKycDto.state.trim(),
            country: createKycDto.country.trim(),
            zipNumber: createKycDto.zipNumber.trim(),
            phoneNumber: createKycDto.phoneNumber.trim(),
            fileName: createKycDto.fileName?.trim(),
            provider: provider,
            userId: userId,
            status: status,
          },
        });

        // Log successful KYC initiation
        this.logKycOperation('INITIATE', userId, {
          provider,
          status: kyc.status,
          requestedStatus: createKycDto.status,
          email: user.email, // Include user email for better tracking
        });

        return this.createKycResponse(kyc);
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to initiate KYC process');
    }
  }

  /**
   * Membuat data KYC baru untuk user (Legacy method)
   * @deprecated Use initiateKyc instead for new implementations
   */
  async create(
    createKycDto: CreateKycDto,
    userId: string,
  ): Promise<KycResponseDto> {
    try {
      // Validasi user exists dan aktif
      const user = await this.validateUserExists(userId);

      // Cek apakah user sudah memiliki KYC
      await this.checkExistingKyc(userId);

      // Validasi data KYC
      this.validateKycData(createKycDto);

      // Determine status - validate and set appropriate status using the utility
      const status = StatusValidatorUtil.determineInitialStatus(
        createKycDto.status,
      );

      // Determine provider - default to MANUAL if not specified
      const provider = createKycDto.provider?.trim() || 'MANUAL';

      // Use transaction to ensure data consistency
      return await this.prisma.$transaction(async (prisma) => {
        // Create KYC data
        const kyc = await prisma.kyc.create({
          data: {
            identityType: createKycDto.identityType,
            identityNumber: createKycDto.identityNumber.trim(),
            birthdate: new Date(createKycDto.birthdate),
            birthplace: createKycDto.birthplace.trim(),
            address: createKycDto.address.trim(),
            state: createKycDto.state.trim(),
            country: createKycDto.country.trim(),
            zipNumber: createKycDto.zipNumber.trim(),
            phoneNumber: createKycDto.phoneNumber.trim(),
            fileName: createKycDto.fileName?.trim(),
            provider: provider,
            userId: userId,
            status: status,
          },
        });

        // Log successful KYC creation
        this.logKycOperation('CREATE', userId, {
          provider,
          status: kyc.status,
          requestedStatus: createKycDto.status,
          email: user.email, // Include user email for better tracking
        });

        return this.createKycResponse(kyc);
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to create KYC');
    }
  }

  /**
   * Mendapatkan data KYC berdasarkan user ID
   */
  async findByUserId(userId: string): Promise<KycResponseDto> {
    try {
      // Validasi user exists
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      return this.createKycResponse(kyc);
    } catch (error) {
      this.handleServiceError(error, 'Failed to retrieve KYC data');
    }
  }

  /**
   * Mendapatkan status KYC
   */
  async getStatus(userId: string): Promise<KycStatusResponseDto> {
    try {
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
        select: {
          status: true,
          updatedAt: true,
        },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      const statusMessage = this.getStatusMessage(kyc.status as KycStatus);

      return new KycStatusResponseDto(
        kyc.status as KycStatus,
        statusMessage,
        kyc.updatedAt,
      );
    } catch (error) {
      this.handleServiceError(error, 'Failed to retrieve KYC status');
    }
  }

  /**
   * Delete KYC data (admin only)
   */
  async delete(userId: string): Promise<{ message: string }> {
    try {
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      await this.prisma.kyc.delete({
        where: { userId },
      });

      return { message: 'Data KYC berhasil dihapus' };
    } catch (error) {
      this.handleServiceError(error, 'Failed to delete KYC data');
    }
  }
  /**
   * Comprehensive user validation with enhanced error handling
   */
  private async validateUserExists(userId: string): Promise<UserEntity> {
    // Input validation
    if (!userId) {
      throw new BadRequestException(ERROR_MESSAGES.USER_ID_REQUIRED);
    }

    if (typeof userId !== 'string') {
      throw new BadRequestException(ERROR_MESSAGES.USER_ID_INVALID);
    }

    if (userId.trim().length === 0) {
      throw new BadRequestException(ERROR_MESSAGES.USER_ID_EMPTY);
    }

    // Sanitize input
    const sanitizedUserId = userId.trim();

    try {
      const user = await this.prisma.user.findUnique({
        where: { id: sanitizedUserId },
        select: {
          id: true,
          status: true,
          email: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new NotFoundException(
          `${ERROR_MESSAGES.USER_NOT_FOUND}: ${sanitizedUserId}`,
        );
      }

      // Enhanced status validation using constants
      if (!VALID_USER_STATUSES.includes(user.status)) {
        throw new ForbiddenException(
          `${ERROR_MESSAGES.USER_NOT_ACTIVE}. Current status: ${user.status}`,
        );
      }

      return user as UserEntity;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      console.error('Database error during user validation:', error);
      throw new InternalServerErrorException('Failed to validate user');
    }
  }

  /**
   * Cek apakah user sudah memiliki KYC
   */
  private async checkExistingKyc(userId: string): Promise<void> {
    const existingKyc = await this.prisma.kyc.findUnique({
      where: { userId },
    });

    if (existingKyc) {
      throw new ConflictException('User sudah memiliki data KYC');
    }
  }

  /**
   * Enhanced KYC data validation with comprehensive business rules
   */
  private validateKycData(kycData: CreateKycDto): void {
    // Validate birthdate with enhanced logic
    if (kycData.birthdate) {
      this.validateBirthdate(kycData.birthdate);
    }

    // Validate identity information
    if (kycData.identityNumber && kycData.identityType) {
      this.validateIdentityNumber(kycData.identityNumber, kycData.identityType);
    }

    // Validate phone number with enhanced rules
    if (kycData.phoneNumber) {
      this.validatePhoneNumber(kycData.phoneNumber);
    }

    // Validate address information
    if (kycData.address) {
      this.validateAddress(kycData.address);
    }

    // Validate location information
    if (kycData.country) {
      this.validateCountry(kycData.country);
    }

    if (kycData.zipNumber) {
      this.validateZipCode(kycData.zipNumber, kycData.country);
    }

    // Validate file information
    if (kycData.fileName) {
      this.validateFileName(kycData.fileName);
    }

    // Validate provider
    if (kycData.provider) {
      this.validateProvider(kycData.provider);
    }
  }

  /**
   * Enhanced birthdate validation
   */
  private validateBirthdate(birthdate: string): void {
    const birthDate = new Date(birthdate);
    const now = new Date();

    // Check if date is valid
    if (isNaN(birthDate.getTime())) {
      throw new BadRequestException('Invalid birthdate format');
    }

    // Check if date is not in the future
    if (birthDate >= now) {
      throw new BadRequestException('Birthdate cannot be in the future');
    }

    // Check minimum age (17 years)
    const minAge = 17;
    const maxAge = 120; // Maximum reasonable age

    const ageInYears = now.getFullYear() - birthDate.getFullYear();
    const monthDiff = now.getMonth() - birthDate.getMonth();
    const dayDiff = now.getDate() - birthDate.getDate();

    let actualAge = ageInYears;
    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
      actualAge--;
    }

    if (actualAge < minAge) {
      throw new BadRequestException(
        `Minimum age requirement is ${minAge} years. Current age: ${actualAge}`,
      );
    }

    if (actualAge > maxAge) {
      throw new BadRequestException(
        `Age seems unrealistic. Please verify birthdate. Calculated age: ${actualAge}`,
      );
    }
  }

  /**
   * Validasi nomor identitas berdasarkan jenisnya
   */
  private validateIdentityNumber(
    identityNumber: string,
    identityType: IdentityType,
  ): void {
    switch (identityType) {
      case IdentityType.KTP:
        if (!/^\d{16}$/.test(identityNumber)) {
          throw new BadRequestException('Nomor KTP harus 16 digit angka');
        }
        break;
      case IdentityType.PASSPORT:
        if (!/^[A-Z]\d{7}$/.test(identityNumber)) {
          throw new BadRequestException(
            'Format passport tidak valid (contoh: ********)',
          );
        }
        break;
      case IdentityType.DRIVER_LICENSE:
        if (!/^[A-Z0-9]{10,15}$/.test(identityNumber)) {
          throw new BadRequestException('Format SIM tidak valid');
        }
        break;
      case IdentityType.SIM:
        if (!/^[A-Z0-9]{10,15}$/.test(identityNumber)) {
          throw new BadRequestException('Format SIM tidak valid');
        }
        break;
    }
  }

  /**
   * Enhanced phone number validation
   */
  private validatePhoneNumber(phoneNumber: string): void {
    const trimmedPhone = phoneNumber.trim();

    // Check if phone number starts with +62 (Indonesia)
    if (!trimmedPhone.startsWith('+62')) {
      throw new BadRequestException(
        'Phone number must use Indonesian country code (+62)',
      );
    }

    // Validate phone number format and length
    const phoneRegex = /^\+62[0-9]{8,13}$/;
    if (!phoneRegex.test(trimmedPhone)) {
      throw new BadRequestException(
        'Invalid Indonesian phone number format. Expected: +62xxxxxxxxx',
      );
    }

    // Check for common invalid patterns
    const invalidPatterns = [
      /^\+620{8,}$/, // All zeros after +62
      /^\+621{8,}$/, // All ones after +62
    ];

    for (const pattern of invalidPatterns) {
      if (pattern.test(trimmedPhone)) {
        throw new BadRequestException('Phone number contains invalid pattern');
      }
    }
  }

  /**
   * Validate address information
   */
  private validateAddress(address: string): void {
    const trimmedAddress = address.trim();

    if (trimmedAddress.length < 10) {
      throw new BadRequestException(
        'Address must be at least 10 characters long',
      );
    }

    if (trimmedAddress.length > 500) {
      throw new BadRequestException('Address must not exceed 500 characters');
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /^(.)\1{9,}$/, // Repeated characters
      /test|dummy|fake|sample/i, // Test data
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(trimmedAddress)) {
        throw new BadRequestException(
          'Address appears to contain invalid or test data',
        );
      }
    }
  }

  /**
   * Validate country information
   */
  private validateCountry(country: string): void {
    const trimmedCountry = country.trim();

    // For now, primarily support Indonesia
    const supportedCountries = ['IDN', 'INDONESIA', 'ID'];

    if (!supportedCountries.includes(trimmedCountry.toUpperCase())) {
      throw new BadRequestException(
        'Currently only Indonesian residents are supported for KYC verification',
      );
    }
  }

  /**
   * Validate ZIP code based on country
   */
  private validateZipCode(zipCode: string, country?: string): void {
    const trimmedZip = zipCode.trim();

    // Indonesian postal code validation
    if (
      !country ||
      country.toUpperCase().includes('IDN') ||
      country.toUpperCase().includes('INDONESIA')
    ) {
      const indonesianZipRegex = /^[0-9]{5}$/;
      if (!indonesianZipRegex.test(trimmedZip)) {
        throw new BadRequestException(
          'Indonesian postal code must be exactly 5 digits',
        );
      }

      // Check for invalid postal codes
      if (trimmedZip === '00000' || trimmedZip === '99999') {
        throw new BadRequestException('Invalid postal code');
      }
    }
  }

  /**
   * Validate file name
   */
  private validateFileName(fileName: string): void {
    const trimmedFileName = fileName.trim();

    // Check file extension
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf'];
    const hasValidExtension = allowedExtensions.some((ext) =>
      trimmedFileName.toLowerCase().endsWith(ext),
    );

    if (!hasValidExtension) {
      throw new BadRequestException(
        `File must have one of these extensions: ${allowedExtensions.join(', ')}`,
      );
    }

    // Check file name length
    if (trimmedFileName.length > 255) {
      throw new BadRequestException('File name must not exceed 255 characters');
    }

    // Check for suspicious file names
    const suspiciousPatterns = [
      /test|dummy|fake|sample/i,
      /^temp/i,
      /\.(exe|bat|cmd|scr|vbs)$/i, // Executable files
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(trimmedFileName)) {
        throw new BadRequestException(
          'File name appears to be invalid or suspicious',
        );
      }
    }
  }

  /**
   * Validate KYC provider
   */
  private validateProvider(provider: string): void {
    const trimmedProvider = provider.trim().toUpperCase();
    const supportedProviders = ['MANUAL', 'SUMSUB'];

    if (!supportedProviders.includes(trimmedProvider)) {
      throw new BadRequestException(
        `Unsupported KYC provider. Supported providers: ${supportedProviders.join(', ')}`,
      );
    }
  }

  /**
   * @deprecated Use StatusValidatorUtil.determineInitialStatus instead
   */
  private determineKycStatus(requestedStatus?: KycStatus): KycStatus {
    return StatusValidatorUtil.determineInitialStatus(requestedStatus);
  }

  /**
   * Get human-readable status message
   */
  private getStatusMessage(status: KycStatus): string {
    return STATUS_MESSAGES[status] || 'Unknown status';
  }

  /**
   * Enhanced centralized error handling for service methods
   * Provides more specific error messages and improved logging
   */
  private handleServiceError(error: any, context: string): never {
    // Create a structured error object for better logging
    const errorDetails = {
      context,
      message: error.message,
      code: error.code,
      timestamp: new Date().toISOString(),
      stack: error.stack,
    };

    // Log the error with detailed context
    this.logger.error(
      `${context}: ${error.message} (${error.code || 'no_code'})`,
      JSON.stringify(errorDetails),
    );

    // Re-throw known exceptions as-is
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException ||
      error instanceof ConflictException ||
      error instanceof ForbiddenException
    ) {
      throw error;
    }

    // Handle Prisma errors with more specific messages
    if (error.code) {
      switch (error.code) {
        case 'P2002':
          // Extract the constraint field from the error message if available
          const field = error.meta?.target?.[0] || 'field';
          throw new ConflictException(
            `Data already exists: duplicate ${field}`,
          );

        case 'P2025':
          throw new NotFoundException('Record not found or already deleted');

        case 'P2003':
          const refField = error.meta?.field_name || 'reference';
          throw new BadRequestException(
            `Invalid reference: ${refField} does not exist`,
          );

        case 'P2014':
          throw new ConflictException('Violation of a required relation');

        case 'P2004':
          throw new InternalServerErrorException('Database constraint failed');

        default:
          this.logger.error(`Unhandled Prisma error: ${error.code}`, {
            error,
            meta: error.meta,
            context,
          });
      }
    }

    // Handle network or connection errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      throw new InternalServerErrorException(
        'Database connection failed. Please try again later.',
      );
    }

    // Default to internal server error with more context
    throw new InternalServerErrorException(
      `${context}. Please try again later. (Error ID: ${Date.now().toString(36)})`,
    );
  }

  /**
   * Create standardized KYC response object
   */
  private createKycResponse(kyc: any): KycResponseDto {
    return new KycResponseDto({
      id: kyc.id,
      identityType: kyc.identityType as IdentityType,
      identityNumber: kyc.identityNumber,
      birthdate: kyc.birthdate,
      birthplace: kyc.birthplace,
      address: kyc.address,
      state: kyc.state,
      country: kyc.country,
      zipNumber: kyc.zipNumber,
      phoneNumber: kyc.phoneNumber,
      status: kyc.status as KycStatus,
      provider: kyc.provider,
      fileName: kyc.fileName,
      userId: kyc.userId,
      createdAt: kyc.createdAt,
      updatedAt: kyc.updatedAt,
    });
  }

  /**
   * Log KYC operation for audit purposes
   */
  private logKycOperation(
    operation: string,
    userId: string,
    details?: Record<string, any>,
  ): void {
    const logData = {
      operation,
      userId,
      timestamp: new Date().toISOString(),
      ...details,
    };

    this.logger.log(`KYC Operation: ${JSON.stringify(logData)}`);
  }
}
