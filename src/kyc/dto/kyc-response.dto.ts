import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { IdentityType, KycStatus } from './create-kyc.dto';

export class KycResponseDto {
  @ApiProperty({
    example: '01234567',
    description: 'ID unik KYC',
  })
  @Expose()
  id: string;

  @ApiProperty({
    enum: IdentityType,
    example: IdentityType.KTP,
    description: 'Jenis identitas',
  })
  @Expose()
  identityType: IdentityType;

  @ApiProperty({
    example: '3201234567890123',
    description: 'Nomor identitas (sebagian disembunyikan untuk keamanan)',
  })
  @Expose()
  @Transform(({ value }) => {
    // Sembunyikan sebagian nomor identitas untuk keamanan
    if (!value || value.length <= 6) return value;
    const visible = value.slice(0, 4);
    const hidden = '*'.repeat(value.length - 6);
    const lastTwo = value.slice(-2);
    return `${visible}${hidden}${lastTwo}`;
  })
  identityNumber: string;

  @ApiProperty({
    example: '1990-01-15T00:00:00.000Z',
    description: 'Tanggal lahir',
  })
  @Expose()
  birthdate: Date;

  @ApiProperty({
    example: 'Jakarta',
    description: 'Tempat lahir',
  })
  @Expose()
  birthplace: string;

  @ApiProperty({
    example: 'Jl. Sudirman No. 123, RT 001/RW 002',
    description: 'Alamat',
  })
  @Expose()
  address: string;

  @ApiProperty({
    example: 'DKI Jakarta',
    description: 'Provinsi/Negara bagian',
  })
  @Expose()
  state: string;

  @ApiProperty({
    example: 'Indonesia',
    description: 'Negara',
  })
  @Expose()
  country: string;

  @ApiProperty({
    example: '12345',
    description: 'Kode pos',
  })
  @Expose()
  zipNumber: string;

  @ApiProperty({
    example: '+6281234567890',
    description: 'Nomor telepon',
  })
  @Expose()
  @Transform(({ value }) => {
    // Sembunyikan sebagian nomor telepon untuk keamanan
    if (!value || value.length <= 6) return value;
    const prefix = value.slice(0, 4);
    const hidden = '*'.repeat(value.length - 7);
    const suffix = value.slice(-3);
    return `${prefix}${hidden}${suffix}`;
  })
  phoneNumber: string;

  @ApiProperty({
    enum: KycStatus,
    example: KycStatus.PENDING,
    description: 'Status verifikasi KYC',
  })
  @Expose()
  status: KycStatus;

  @ApiProperty({
    example: 'sumadi',
    description: 'Provider layanan KYC',
    required: false,
  })
  @Expose()
  provider?: string;

  @ApiProperty({
    example: 'ktp_scan_123456789.jpg',
    description: 'Nama file dokumen',
    required: false,
  })
  @Expose()
  fileName?: string;

  @ApiProperty({
    example: '01234567',
    description: 'ID user yang memiliki KYC ini',
  })
  @Expose()
  userId: string;

  @ApiProperty({
    example: '2023-01-15T10:00:00.000Z',
    description: 'Tanggal dibuat',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-16T15:30:00.000Z',
    description: 'Tanggal terakhir diupdate',
  })
  @Expose()
  updatedAt: Date;

  constructor(data: Partial<KycResponseDto>) {
    Object.assign(this, data);
  }
}

export class KycStatusResponseDto {
  @ApiProperty({
    example: KycStatus.PENDING,
    description: 'Status KYC saat ini',
  })
  status: KycStatus;

  @ApiProperty({
    example: 'KYC sedang dalam proses verifikasi',
    description: 'Pesan status',
  })
  message: string;

  @ApiProperty({
    example: '2023-01-16T15:30:00.000Z',
    description: 'Tanggal terakhir diupdate',
  })
  updatedAt: Date;

  constructor(status: KycStatus, message: string, updatedAt: Date) {
    this.status = status;
    this.message = message;
    this.updatedAt = updatedAt;
  }
}
