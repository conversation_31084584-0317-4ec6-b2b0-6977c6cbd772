import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from './common/pipes/validation.pipe';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export async function startApiService() {
  const app = await NestFactory.create(AppModule, {
    logger:
      process.env.NODE_ENV === 'production'
        ? ['error', 'warn', 'log']
        : ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // Enable graceful shutdown hooks
  app.enableShutdownHooks();

  app.useGlobalPipes(new ValidationPipe());
  app.useGlobalFilters(new HttpExceptionFilter());

  app.setGlobalPrefix('api/v1');

  // CORS configuration
  app.enableCors({
    origin:
      process.env.NODE_ENV === 'production'
        ? [
            'https://your-frontend-domain.com',
            'https://orbitum-app.fly.dev', // Allow self-origin for Swagger
          ]
        : true, // Allow all origins in development
    credentials: true,
  });

  // Swagger configuration - TERSEDIA DI SEMUA ENVIRONMENT
  const config = new DocumentBuilder()
    .setTitle('Orbitum API')
    .setDescription('User management and KYC/KYB API')
    .setVersion('1.0')
    .addTag('Users', 'User management endpoints')
    .addTag('Authentication', 'Authentication endpoints')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addServer(
      process.env.NODE_ENV === 'production'
        ? 'https://orbitum-app.fly.dev'
        : 'http://localhost:3000',
      process.env.NODE_ENV === 'production'
        ? 'Production server'
        : 'Development server',
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Setup Swagger dengan custom options
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
      docExpansion: 'none',
      filter: true,
      showRequestDuration: true,
    },
    customSiteTitle: 'Orbitum API Documentation',
    customfavIcon: '/favicon.ico',
    customJs: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-standalone-preset.min.js',
    ],
    customCssUrl: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css',
    ],
  });

  // Health check endpoint
  app.getHttpAdapter().get('/api/v1/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
      swagger: `${req.protocol}://${req.get('host')}/api/docs`,
      role: 'api',
    });
  });

  // API Info endpoint
  app.getHttpAdapter().get('/api/v1/info', (req, res) => {
    res.json({
      name: 'Orbitum API',
      version: '1.0.0',
      description: 'User management and KYC/KYB API',
      environment: process.env.NODE_ENV || 'development',
      role: 'api',
      endpoints: {
        health: `${req.protocol}://${req.get('host')}/api/v1/health`,
        docs: `${req.protocol}://${req.get('host')}/api/docs`,
        register: `${req.protocol}://${req.get('host')}/api/v1/users/register`,
      },
    });
  });

  const port = process.env.PORT || 3000;

  try {
    await app.listen(port, '0.0.0.0');

    console.log(`🚀 API Server is running on port ${port}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`📚 Swagger docs: http://localhost:${port}/api/docs`);
    console.log(`❤️ Health check: http://localhost:${port}/api/v1/health`);
    console.log(`ℹ️ API info: http://localhost:${port}/api/v1/info`);

    // Production-specific logs
    if (process.env.NODE_ENV === 'production') {
      console.log(`🌐 Production URLs:`);
      console.log(`   📚 Swagger: https://orbitum-app.fly.dev/api/docs`);
      console.log(`   ❤️ Health: https://orbitum-app.fly.dev/api/v1/health`);
      console.log(
        `   👤 Register: https://orbitum-app.fly.dev/api/v1/users/register`,
      );
    }

    // Setup graceful shutdown handlers
    setupGracefulShutdown(app);
  } catch (error) {
    if (error.code === 'EADDRINUSE') {
      console.error(`❌ Port ${port} is already in use`);
      console.log(`💡 Try running: lsof -ti:${port} | xargs kill -9`);
    } else {
      console.error('❌ Failed to start API server:', error);
    }
    process.exit(1);
  }
}

// Graceful shutdown function
function setupGracefulShutdown(app: any) {
  const gracefulShutdown = async (signal: string) => {
    console.log(
      `🛑 ${signal} received, shutting down API server gracefully...`,
    );

    try {
      // Close the NestJS application
      await app.close();
      console.log('✅ API server closed successfully');

      // Exit the process
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during API server shutdown:', error);
      process.exit(1);
    }
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  // Handle uncaught exceptions with restart capability
  process.on('uncaughtException', async (error) => {
    console.error('❌ Uncaught Exception:', error);

    // Check if it's an OAuth-related error that should trigger restart
    if (isOAuthRelatedError(error)) {
      console.log('🔄 OAuth-related error detected, attempting restart...');
      await attemptRestart('OAUTH_UNCAUGHT_EXCEPTION', error);
    } else {
      gracefulShutdown('UNCAUGHT_EXCEPTION');
    }
  });

  // Handle unhandled promise rejections with restart capability
  process.on('unhandledRejection', async (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);

    // Check if it's an OAuth-related error that should trigger restart
    if (isOAuthRelatedError(reason)) {
      console.log('🔄 OAuth-related rejection detected, attempting restart...');
      await attemptRestart('OAUTH_UNHANDLED_REJECTION', reason);
    } else {
      gracefulShutdown('UNHANDLED_REJECTION');
    }
  });

  console.log('🔧 Graceful shutdown handlers registered for API server');
}

// Helper function to detect OAuth-related errors
function isOAuthRelatedError(error: any): boolean {
  if (!error) return false;

  const errorString = error.toString().toLowerCase();
  const errorMessage = error.message?.toLowerCase() || '';
  const errorStack = error.stack?.toLowerCase() || '';

  const oauthKeywords = [
    'oauth',
    'google',
    'authentication',
    'auth/google',
    'frontend_url',
    'redirect',
    'callback',
    'passport',
  ];

  return oauthKeywords.some(
    (keyword) =>
      errorString.includes(keyword) ||
      errorMessage.includes(keyword) ||
      errorStack.includes(keyword),
  );
}

// Helper function to attempt restart with port cleanup
async function attemptRestart(reason: string, error: any): Promise<void> {
  try {
    console.log(`🔄 Attempting restart due to: ${reason}`);

    // Kill processes on current port
    const port = process.env.PORT || 3000;
    await killProcessesOnPort(Number(port));

    // Wait a moment before restart
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Log restart attempt
    console.log('🔄 Restarting API server...');

    // Exit with code 0 to indicate restart (handled by process manager)
    process.exit(0);
  } catch (restartError) {
    console.error('❌ Failed to restart API server:', restartError);
    // Fall back to graceful shutdown
    process.exit(1);
  }
}

// Helper function to kill processes on port
async function killProcessesOnPort(port: number): Promise<void> {
  try {
    console.log(`🔧 Cleaning up processes on port ${port}...`);

    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    if (process.platform === 'win32') {
      // Windows
      try {
        await execAsync(`netstat -ano | findstr :${port}`);
        await execAsync(
          `for /f "tokens=5" %a in ('netstat -ano ^| findstr :${port}') do taskkill /PID %a /F`,
        );
      } catch (error) {
        // Ignore errors - might mean no processes found
      }
    } else {
      // Unix/Linux/macOS
      try {
        const { stdout } = await execAsync(`lsof -ti:${port}`);
        if (stdout.trim()) {
          await execAsync(`lsof -ti:${port} | xargs kill -9`);
        }
      } catch (error) {
        // Ignore errors - might mean no processes found
      }
    }

    console.log(`✅ Port ${port} cleanup completed`);
  } catch (error) {
    console.error(`❌ Failed to cleanup port ${port}:`, error.message);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  startApiService().catch((error) => {
    console.error('❌ Failed to start API server:', error);
    process.exit(1);
  });
}
