import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  OAuthRedirectType,
  OAuthSuccessData,
  OAuthErrorData,
  OAuthAccountLinkingData,
  OAuthRedirectOptions,
} from '../dto/oauth-redirect.dto';

export interface RedirectConfig {
  url: string;
  isValid: boolean;
  error?: string;
}

export interface TimerConfig {
  enabled: boolean;
  delay: number;
  showCountdown: boolean;
}

@Injectable()
export class OAuthRedirectManagerService {
  private readonly logger = new Logger(OAuthRedirectManagerService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Generate comprehensive redirect JavaScript with proper URL handling
   */
  generateRedirectScript(
    type: OAuthRedirectType,
    data: OAuthSuccessData | OAuthErrorData | OAuthAccountLinkingData,
    options: OAuthRedirectOptions,
    frontendUrl: string,
  ): string {
    const redirectConfig = this.getRedirectConfig(type, data);
    const timerConfig = this.getTimerConfig(type, options);

    return `
      // OAuth redirect configuration
      const oauthData = ${JSON.stringify(data)};
      const oauthOptions = ${JSON.stringify(options)};
      const frontendBaseUrl = '${this.escapeForJs(frontendUrl)}';
      const redirectConfig = ${JSON.stringify(redirectConfig)};
      const timerConfig = ${JSON.stringify(timerConfig)};

      // Enhanced token accessibility - Make tokens globally accessible
      window.oauthTokens = {
        accessToken: oauthData.accessToken || null,
        refreshToken: oauthData.refreshToken || null,
        accessTokenExpiresIn: oauthData.accessTokenExpiresIn || null,
        refreshTokenExpiresIn: oauthData.refreshTokenExpiresIn || null,
        tokenType: oauthData.tokenType || 'Bearer',
        user: oauthData.user || null,
        isNewUser: oauthData.isNewUser || false,
        provider: oauthData.provider || 'google',
        expiresAt: oauthData.accessTokenExpiresIn ?
          new Date(Date.now() + (oauthData.accessTokenExpiresIn * 1000)).toISOString() : null
      };

      // Store tokens in localStorage for persistence (optional)
      if (oauthData.accessToken && typeof(Storage) !== "undefined") {
        try {
          localStorage.setItem('oauth_access_token', oauthData.accessToken);
          localStorage.setItem('oauth_refresh_token', oauthData.refreshToken || '');
          localStorage.setItem('oauth_user', JSON.stringify(oauthData.user || {}));
          localStorage.setItem('oauth_expires_at', window.oauthTokens.expiresAt || '');
          localStorage.setItem('oauth_provider', oauthData.provider || 'google');
        } catch (storageError) {
          console.warn('Could not store tokens in localStorage:', storageError);
        }
      }

      // Dispatch custom event for token availability
      try {
        const tokenEvent = new CustomEvent('oauthTokensReady', {
          detail: window.oauthTokens
        });
        window.dispatchEvent(tokenEvent);
      } catch (eventError) {
        console.warn('Could not dispatch token event:', eventError);
      }

      // Utility functions for easy token access
      window.getOAuthTokens = function() {
        return window.oauthTokens;
      };

      window.getAccessToken = function() {
        return window.oauthTokens ? window.oauthTokens.accessToken : null;
      };

      window.getRefreshToken = function() {
        return window.oauthTokens ? window.oauthTokens.refreshToken : null;
      };

      window.getOAuthUser = function() {
        return window.oauthTokens ? window.oauthTokens.user : null;
      };

      window.isTokenExpired = function() {
        if (!window.oauthTokens || !window.oauthTokens.expiresAt) {
          return true;
        }
        return new Date() >= new Date(window.oauthTokens.expiresAt);
      };

      // Enhanced redirect function with error handling
      function redirectToFrontend(url) {
        try {
          console.log('Redirecting to:', url);
          
          // Validate URL before redirect
          if (!url || typeof url !== 'string') {
            console.error('Invalid redirect URL:', url);
            url = frontendBaseUrl;
          }

          if (oauthOptions.isPopup && window.opener) {
            // Handle popup flow with enhanced token data
            try {
              const messageData = {
                type: 'OAUTH_${type}',
                data: oauthData,
                tokens: window.oauthTokens,
                success: ${type === OAuthRedirectType.SUCCESS || type === OAuthRedirectType.ACCOUNT_LINKING},
                redirectUrl: url,
                timestamp: new Date().toISOString()
              };

              window.opener.postMessage(messageData, frontendBaseUrl);

              // Also try posting to parent if available (for iframe scenarios)
              if (window.parent && window.parent !== window) {
                window.parent.postMessage(messageData, frontendBaseUrl);
              }

              window.close();
            } catch (postMessageError) {
              console.error('PostMessage failed:', postMessageError);
              // Fallback to direct redirect
              window.location.href = url;
            }
          } else {
            // Handle redirect flow
            window.location.href = url;
          }
        } catch (error) {
          console.error('Redirect error:', error);
          // Ultimate fallback
          try {
            window.location.href = frontendBaseUrl;
          } catch (fallbackError) {
            console.error('Fallback redirect failed:', fallbackError);
            alert('Redirect failed. Please manually navigate to the application.');
          }
        }
      }

      // Enhanced timer with proper URL construction
      function initializeTimer() {
        if (!timerConfig.enabled) {
          return;
        }

        let countdown = Math.floor(timerConfig.delay / 1000);
        let countdownElement = null;

        if (timerConfig.showCountdown) {
          countdownElement = document.createElement('p');
          countdownElement.className = 'countdown';
          countdownElement.style.cssText = 'color: #6b7280; font-size: 0.875rem; margin-top: 1rem;';
          
          const contentElement = document.querySelector('.content');
          if (contentElement) {
            contentElement.appendChild(countdownElement);
          }
        }

        const updateCountdown = () => {
          try {
            if (countdownElement) {
              countdownElement.textContent = \`Redirecting in \${countdown} seconds...\`;
            }

            if (countdown <= 0) {
              // Use the validated redirect URL from config
              const targetUrl = redirectConfig.isValid ? redirectConfig.url : frontendBaseUrl;
              console.log('Timer expired, redirecting to:', targetUrl);
              redirectToFrontend(targetUrl);
            } else {
              countdown--;
              setTimeout(updateCountdown, 1000);
            }
          } catch (error) {
            console.error('Timer update error:', error);
            // Fallback redirect
            redirectToFrontend(frontendBaseUrl);
          }
        };

        // Start the countdown
        setTimeout(updateCountdown, 100);
      }

      // Manual redirect button handler
      function setupManualRedirect() {
        const buttons = document.querySelectorAll('[onclick*="redirectToFrontend"]');
        buttons.forEach(button => {
          button.addEventListener('click', (event) => {
            event.preventDefault();
            const targetUrl = redirectConfig.isValid ? redirectConfig.url : frontendBaseUrl;
            redirectToFrontend(targetUrl);
          });
        });
      }

      // Initialize redirect functionality
      function initializeRedirect() {
        try {
          // Prevent back navigation
          history.pushState(null, null, window.location.href);
          window.addEventListener('popstate', function(event) {
            history.pushState(null, null, window.location.href);
          });

          // Initialize timer if enabled
          initializeTimer();

          // Setup manual redirect buttons
          setupManualRedirect();

          console.log('OAuth redirect initialized successfully');
        } catch (error) {
          console.error('Failed to initialize redirect:', error);
          // Emergency fallback
          setTimeout(() => {
            window.location.href = frontendBaseUrl;
          }, 5000);
        }
      }

      // Start initialization when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeRedirect);
      } else {
        initializeRedirect();
      }
    `;
  }

  /**
   * Get redirect configuration based on OAuth type and data
   */
  private getRedirectConfig(
    type: OAuthRedirectType,
    data: OAuthSuccessData | OAuthErrorData | OAuthAccountLinkingData,
  ): RedirectConfig {
    try {
      let url: string;

      switch (type) {
        case OAuthRedirectType.SUCCESS:
          url = (data as OAuthSuccessData).redirectUrl;
          break;
        case OAuthRedirectType.ACCOUNT_LINKING:
          url = (data as OAuthAccountLinkingData).redirectUrl;
          break;
        case OAuthRedirectType.ERROR:
          url = (data as OAuthErrorData).redirectUrl;
          break;
        default:
          url = this.getFallbackUrl();
      }

      // Validate the URL
      if (!url || typeof url !== 'string') {
        return {
          url: this.getFallbackUrl(),
          isValid: false,
          error: 'Invalid or missing redirect URL',
        };
      }

      // Ensure URL is properly formatted
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        return {
          url: this.getFallbackUrl(),
          isValid: false,
          error: 'Redirect URL must be absolute',
        };
      }

      return {
        url,
        isValid: true,
      };
    } catch (error) {
      this.logger.error('Error getting redirect config:', error);
      return {
        url: this.getFallbackUrl(),
        isValid: false,
        error: error.message,
      };
    }
  }

  /**
   * Get timer configuration based on OAuth type and options
   */
  private getTimerConfig(type: OAuthRedirectType, options: OAuthRedirectOptions): TimerConfig {
    // Disable timer for error pages by default
    if (type === OAuthRedirectType.ERROR) {
      return {
        enabled: false,
        delay: 0,
        showCountdown: false,
      };
    }

    return {
      enabled: options.autoRedirect ?? true,
      delay: options.redirectDelay ?? 3000,
      showCountdown: true,
    };
  }

  /**
   * Get fallback URL when primary redirect fails
   */
  private getFallbackUrl(): string {
    return this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000');
  }

  /**
   * Escape string for safe use in JavaScript
   */
  private escapeForJs(str: string): string {
    return str
      .replace(/\\/g, '\\\\')
      .replace(/'/g, "\\'")
      .replace(/"/g, '\\"')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t');
  }

  /**
   * Validate redirect URL format and security
   */
  validateRedirectUrl(url: string): { isValid: boolean; error?: string } {
    try {
      if (!url || typeof url !== 'string') {
        return { isValid: false, error: 'URL is required and must be a string' };
      }

      const parsedUrl = new URL(url);
      const frontendUrl = this.configService.get<string>('FRONTEND_URL');
      
      if (frontendUrl) {
        const frontendOrigin = new URL(frontendUrl).origin;
        if (parsedUrl.origin !== frontendOrigin) {
          return { isValid: false, error: 'URL origin does not match FRONTEND_URL' };
        }
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: `Invalid URL format: ${error.message}` };
    }
  }

  /**
   * Generate manual redirect button with proper URL
   */
  generateManualRedirectButton(
    type: OAuthRedirectType,
    data: OAuthSuccessData | OAuthErrorData | OAuthAccountLinkingData,
    buttonText: string = 'Continue to App',
  ): string {
    const redirectConfig = this.getRedirectConfig(type, data);
    const targetUrl = redirectConfig.isValid ? redirectConfig.url : this.getFallbackUrl();

    return `
      <button 
        onclick="redirectToFrontend('${this.escapeForJs(targetUrl)}')" 
        class="btn btn-primary"
        type="button"
      >
        ${buttonText}
      </button>
    `;
  }
}
