import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { TokenManagementService } from './token-management.service';
import { DeviceInfoDto } from '../dto/device-info.dto';
import { TokenPairDto } from '../dto/token-pair.dto';

@Injectable()
export class TokenRotationService {
  private readonly logger = new Logger(TokenRotationService.name);

  constructor(
    private readonly tokenManager: TokenManagementService,
  ) {}

  /**
   * Implement token rotation on refresh
   */
  async rotateRefreshToken(
    oldRefreshToken: string,
    deviceInfo: DeviceInfoDto,
  ): Promise<TokenPairDto> {
    const refreshTokenRecord =
      await this.validateAndGetRefreshToken(oldRefreshToken);

    if (!refreshTokenRecord) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const user = refreshTokenRecord.user;

    // Revoke old refresh token
    await this.tokenManager.revokeRefreshToken(oldRefreshToken);

    // Generate new token pair
    const newTokenPair = await this.tokenManager.generateTokenPair(
      user,
      deviceInfo,
    );

    this.logger.log(`Token rotated for user ${user.id}`);

    return newTokenPair;
  }

  /**
   * Validate device fingerprint for security
   */
  async validateDeviceFingerprint(
    refreshToken: string,
    currentDeviceInfo: DeviceInfoDto,
  ): Promise<boolean> {
    const tokenRecord = await this.validateAndGetRefreshToken(refreshToken);

    if (!tokenRecord) return false;

    // Basic device validation (can be enhanced)
    return (
      tokenRecord.userAgent === currentDeviceInfo.userAgent &&
      tokenRecord.ipAddress === currentDeviceInfo.ipAddress
    );
  }

  private async validateAndGetRefreshToken(token: string): Promise<any> {
    return this.tokenManager.validateRefreshToken(token);
  }
}
