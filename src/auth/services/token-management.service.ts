import { Injectable, UnauthorizedException } from '@nestjs/common';
import * as crypto from 'crypto';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../../prisma/prisma.service';
import { DeviceInfoDto } from '../dto/device-info.dto';
import { TokenPairDto } from '../dto/token-pair.dto';

@Injectable()
export class TokenManagementService {
  // Token Configuration
  private readonly ACCESS_TOKEN_EXPIRY = '15m';
  private readonly REFRESH_TOKEN_EXPIRY_DAYS = 30;
  private readonly ACCESS_TOKEN_EXPIRY_SECONDS = 15 * 60;
  private readonly REFRESH_TOKEN_EXPIRY_SECONDS = 30 * 24 * 60 * 60;

  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * Generate token pair (access + refresh)
   */
  async generateTokenPair(
    user: any,
    deviceInfo: DeviceInfoDto,
  ): Promise<TokenPairDto> {
    const payload = this.createJwtPayload(user);
    const accessToken = this.generateAccessToken(payload);
    const refreshToken = await this.createRefreshToken(user.id, deviceInfo);

    return new TokenPairDto({
      accessToken,
      refreshToken: refreshToken.token,
      accessTokenExpiresIn: this.ACCESS_TOKEN_EXPIRY_SECONDS,
      refreshTokenExpiresIn: this.REFRESH_TOKEN_EXPIRY_SECONDS,
      tokenType: 'Bearer',
    });
  }

  /**
   * Create refresh token in database
   */
  private async createRefreshToken(
    userId: string,
    deviceInfo: DeviceInfoDto,
  ): Promise<any> {
    const token = this.generateSecureToken(64);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + this.REFRESH_TOKEN_EXPIRY_DAYS);

    return this.prisma.refreshToken.create({
      data: {
        token,
        userId,
        deviceId: deviceInfo.deviceId,
        userAgent: deviceInfo.userAgent,
        ipAddress: deviceInfo.ipAddress,
        expiresAt,
      },
    });
  }

  /**
   * Validate and refresh access token
   */
  async refreshAccessToken(refreshTokenValue: string): Promise<TokenPairDto> {
    const refreshToken = await this.validateRefreshToken(refreshTokenValue);
    if (!refreshToken) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const user = refreshToken.user;
    const payload = this.createJwtPayload(user);
    const newAccessToken = this.generateAccessToken(payload);

    // Update last used timestamp
    await this.prisma.refreshToken.update({
      where: { id: refreshToken.id },
      data: { lastUsedAt: new Date() },
    });

    return new TokenPairDto({
      accessToken: newAccessToken,
      refreshToken: refreshTokenValue, // Keep same refresh token
      accessTokenExpiresIn: this.ACCESS_TOKEN_EXPIRY_SECONDS,
      refreshTokenExpiresIn: this.REFRESH_TOKEN_EXPIRY_SECONDS,
      tokenType: 'Bearer',
    });
  }

  /**
   * Revoke refresh token
   */
  async revokeRefreshToken(refreshTokenValue: string): Promise<void> {
    await this.prisma.refreshToken.updateMany({
      where: {
        token: refreshTokenValue,
        isRevoked: false,
      },
      data: {
        isRevoked: true,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * Revoke all refresh tokens for user
   */
  async revokeAllUserTokens(userId: string): Promise<void> {
    await this.prisma.refreshToken.updateMany({
      where: {
        userId,
        isRevoked: false,
      },
      data: {
        isRevoked: true,
        updatedAt: new Date(),
      },
    });

    // Increment token version to invalidate all access tokens
    await this.prisma.user.update({
      where: { id: userId },
      data: { tokenVersion: { increment: 1 } },
    });
  }

  /**
   * Blacklist access token
   */
  async blacklistAccessToken(
    token: string,
    userId: string,
    reason?: string,
  ): Promise<void> {
    const tokenHash = this.hashToken(token);
    const decoded = this.jwtService.decode(token) as any;
    const expiresAt = new Date(decoded.exp * 1000);

    await this.prisma.accessTokenBlacklist.create({
      data: {
        tokenHash,
        userId,
        expiresAt,
        reason,
      },
    });
  }

  /**
   * Check if access token is blacklisted
   */
  async isAccessTokenBlacklisted(token: string): Promise<boolean> {
    const tokenHash = this.hashToken(token);
    const blacklistedToken = await this.prisma.accessTokenBlacklist.findUnique({
      where: { tokenHash },
    });

    return !!blacklistedToken && blacklistedToken.expiresAt > new Date();
  }

  /**
   * Clean up expired tokens
   */
  async cleanupExpiredTokens(): Promise<void> {
    const now = new Date();

    // Remove expired refresh tokens
    await this.prisma.refreshToken.deleteMany({
      where: {
        OR: [
          { expiresAt: { lt: now } },
          {
            isRevoked: true,
            updatedAt: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) },
          },
        ],
      },
    });

    // Remove expired access token blacklist entries
    await this.prisma.accessTokenBlacklist.deleteMany({
      where: { expiresAt: { lt: now } },
    });
  }

  // Private utility methods
  private createJwtPayload(user: any): any {
    return {
      sub: user.id,
      email: user.email,
      username: user.username,
      type: user.type,
      status: user.status,
      tokenVersion: user.tokenVersion || 0,
      iat: Math.floor(Date.now() / 1000),
    };
  }

  private generateAccessToken(payload: any): string {
    return this.jwtService.sign(payload, {
      expiresIn: this.ACCESS_TOKEN_EXPIRY,
    });
  }

  private generateSecureToken(length: number = 64): string {
    return crypto.randomBytes(length).toString('hex');
  }

  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  async validateRefreshToken(token: string): Promise<any> {
    return this.prisma.refreshToken.findFirst({
      where: {
        token,
        isRevoked: false,
        expiresAt: { gte: new Date() },
      },
      include: { user: true },
    });
  }
}
