import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { UserResponseDto } from '../../users/dto/user-response.dto';

export enum OAuthRedirectType {
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  ACCOUNT_LINKING = 'ACCOUNT_LINKING',
}

export enum OAuthErrorType {
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  INVALID_USER_DATA = 'INVALID_USER_DATA',
  ACCOUNT_LINKING_CONFLICT = 'ACCOUNT_LINKING_CONFLICT',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  INVALID_OAUTH_DATA = 'INVALID_OAUTH_DATA',
  ACCOUNT_INACTIVE = 'ACCOUNT_INACTIVE',
}

export interface OAuthRedirectOptions {
  autoRedirect?: boolean;
  redirectDelay?: number;
  showManualRedirect?: boolean;
  isPopup?: boolean;
}

export class OAuthSuccessData {
  @ApiProperty({
    description: 'JWT access token for authentication',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token for token renewal',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;

  @ApiProperty({
    description: 'Access token expiration time in seconds',
    example: 900,
  })
  accessTokenExpiresIn: number;

  @ApiProperty({
    description: 'Refresh token expiration time in seconds',
    example: 2592000,
  })
  refreshTokenExpiresIn: number;

  @ApiProperty({
    description: 'Token type',
    example: 'Bearer',
    default: 'Bearer',
  })
  tokenType: string = 'Bearer';

  @ApiProperty({
    description: 'User information',
    type: UserResponseDto,
  })
  @ValidateNested()
  @Type(() => UserResponseDto)
  user: UserResponseDto;

  @ApiProperty({
    description: 'Frontend URL to redirect to after successful authentication',
    example: 'http://localhost:3000/auth/google/success',
  })
  @IsString()
  @IsNotEmpty()
  redirectUrl: string;

  @ApiProperty({
    description: 'Whether this is a new user registration',
    example: false,
  })
  isNewUser: boolean;

  @ApiProperty({
    description: 'Success message to display',
    example: 'Authentication successful! Redirecting...',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  constructor(partial: Partial<OAuthSuccessData>) {
    Object.assign(this, partial);
  }
}

export class OAuthErrorData {
  @ApiProperty({
    enum: OAuthErrorType,
    description: 'Type of OAuth error that occurred',
  })
  @IsEnum(OAuthErrorType)
  errorType: OAuthErrorType;

  @ApiProperty({
    description: 'Human-readable error message',
    example: 'Authentication failed. Please try again.',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Technical error details for debugging',
    example: 'Invalid OAuth provider response',
    required: false,
  })
  @IsString()
  @IsOptional()
  details?: string;

  @ApiProperty({
    description: 'Frontend URL to redirect to after error',
    example: 'http://localhost:3000/auth/error',
  })
  @IsString()
  @IsNotEmpty()
  redirectUrl: string;

  @ApiProperty({
    description: 'Whether user can retry the authentication',
    example: true,
  })
  canRetry: boolean;

  @ApiProperty({
    description: 'URL to retry authentication',
    example: 'http://localhost:3001/api/v1/auth/google',
    required: false,
  })
  @IsString()
  @IsOptional()
  retryUrl?: string;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2024-01-15T10:30:00.000Z',
  })
  timestamp: string;

  constructor(partial: Partial<OAuthErrorData>) {
    Object.assign(this, partial);
    this.timestamp = this.timestamp || new Date().toISOString();
  }
}

export class OAuthAccountLinkingData {
  @ApiProperty({
    description: 'Existing user information',
    type: UserResponseDto,
  })
  @ValidateNested()
  @Type(() => UserResponseDto)
  existingUser: UserResponseDto;

  @ApiProperty({
    description: 'OAuth provider being linked',
    example: 'google',
  })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({
    description: 'Message about account linking',
    example: 'Your Google account has been linked to your existing account.',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Frontend URL to redirect to after linking',
    example: 'http://localhost:3000/profile/linked',
  })
  @IsString()
  @IsNotEmpty()
  redirectUrl: string;

  @ApiProperty({
    description: 'JWT access token for the linked account',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  accessToken: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 86400,
  })
  expiresIn: number;

  constructor(partial: Partial<OAuthAccountLinkingData>) {
    Object.assign(this, partial);
  }
}

export class OAuthRedirectResponseDto {
  @ApiProperty({
    enum: OAuthRedirectType,
    description: 'Type of OAuth redirect response',
  })
  @IsEnum(OAuthRedirectType)
  type: OAuthRedirectType;

  @ApiProperty({
    description: 'Success data for successful authentication',
    type: OAuthSuccessData,
    required: false,
  })
  @ValidateNested()
  @Type(() => OAuthSuccessData)
  @IsOptional()
  successData?: OAuthSuccessData;

  @ApiProperty({
    description: 'Error data for failed authentication',
    type: OAuthErrorData,
    required: false,
  })
  @ValidateNested()
  @Type(() => OAuthErrorData)
  @IsOptional()
  errorData?: OAuthErrorData;

  @ApiProperty({
    description: 'Account linking data for linked accounts',
    type: OAuthAccountLinkingData,
    required: false,
  })
  @ValidateNested()
  @Type(() => OAuthAccountLinkingData)
  @IsOptional()
  accountLinkingData?: OAuthAccountLinkingData;

  @ApiProperty({
    description: 'Redirect options for customizing behavior',
    required: false,
  })
  @IsObject()
  @IsOptional()
  options?: OAuthRedirectOptions;

  constructor(partial: Partial<OAuthRedirectResponseDto>) {
    Object.assign(this, partial);
  }
}

// Utility interfaces for internal use
export interface HTMLTemplateData {
  title: string;
  data: OAuthSuccessData | OAuthErrorData | OAuthAccountLinkingData;
  options: OAuthRedirectOptions;
  frontendUrl: string;
}

export interface SecurityValidationResult {
  isValid: boolean;
  error?: string;
}

// Enhanced typing for OAuth redirect handling
export interface OAuthRedirectContext {
  userId?: string;
  userEmail?: string;
  oauthProvider?: string;
  requestId?: string;
  userAgent?: string;
  ipAddress?: string;
  timestamp: string;
  sessionId?: string;
}

export interface RedirectValidationResult {
  isValid: boolean;
  sanitizedUrl?: string;
  error?: string;
  warnings?: string[];
}

export interface TimerConfiguration {
  enabled: boolean;
  delay: number;
  showCountdown: boolean;
  onComplete?: () => void;
  onError?: (error: Error) => void;
}

export interface RedirectConfiguration {
  url: string;
  isValid: boolean;
  fallbackUrl?: string;
  error?: string;
  metadata?: Record<string, any>;
}

// Type guards for OAuth data
export function isOAuthSuccessData(data: any): data is OAuthSuccessData {
  return data && typeof data === 'object' && 'accessToken' in data && 'user' in data;
}

export function isOAuthErrorData(data: any): data is OAuthErrorData {
  return data && typeof data === 'object' && 'errorType' in data && 'message' in data;
}

export function isOAuthAccountLinkingData(data: any): data is OAuthAccountLinkingData {
  return data && typeof data === 'object' && 'existingUser' in data && 'provider' in data;
}

// New OAuth API Response DTO that matches LoginResponseDto structure
export class OAuthApiResponseDto {
  @ApiProperty({
    description: 'JWT access token for authentication',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token for token renewal',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;

  @ApiProperty({
    description: 'Access token expiration time in seconds',
    example: 900,
  })
  accessTokenExpiresIn: number;

  @ApiProperty({
    description: 'Refresh token expiration time in seconds',
    example: 2592000,
  })
  refreshTokenExpiresIn: number;

  @ApiProperty({
    description: 'Token type',
    example: 'Bearer',
    default: 'Bearer',
  })
  tokenType: string = 'Bearer';

  @ApiProperty({
    description: 'User information',
    type: UserResponseDto,
  })
  @ValidateNested()
  @Type(() => UserResponseDto)
  user: UserResponseDto;

  @ApiProperty({
    description: 'Whether this is a new user registration',
    example: false,
  })
  isNewUser: boolean;

  @ApiProperty({
    description: 'OAuth provider used for authentication',
    example: 'google',
  })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({
    description: 'Success message',
    example: 'OAuth authentication successful',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  constructor(partial: Partial<OAuthApiResponseDto>) {
    Object.assign(this, partial);
  }
}
