import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UsersModule } from '../users/users.module';
import { PrismaModule } from '../prisma/prisma.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LocalStrategy } from './strategies/local.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';
import { GoogleStrategy } from './strategies/google.strategy';
import { OAuthHtmlTemplateService } from './services/oauth-html-template.service';
import { OAuthSecurityService } from './services/oauth-security.service';
import { OAuthErrorHandlerService } from './services/oauth-error-handler.service';
import { OAuthRedirectManagerService } from './services/oauth-redirect-manager.service';
import { FrontendValidationService } from './services/frontend-validation.service';
import { TokenManagementService } from './services/token-management.service';
import { TokenRotationService } from './services/token-rotation.service';
import { TokenSecurityService } from './services/token-security.service';
import { TokenErrorHandlerService } from './services/token-error-handler.service';
import { OAuthResponseHandlerService } from './services/oauth-response-handler.service';
import { CorsHandlerService } from './services/cors-handler.service';
import { RateLimiterService } from './services/rate-limiter.service';
import { AuditLoggingService } from './services/audit-logging.service';
import { ErrorMonitoringService } from './services/error-monitoring.service';
import { InputValidationService } from './services/input-validation.service';
import { TokenValidationController } from './controllers/token-validation.controller';

@Module({
  imports: [
    UsersModule,
    PrismaModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'your-secret-key',
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController, TokenValidationController],
  providers: [
    AuthService,
    LocalStrategy,
    JwtStrategy,
    GoogleStrategy,
    TokenManagementService,
    TokenRotationService,
    TokenSecurityService,
    TokenErrorHandlerService,
    OAuthHtmlTemplateService,
    OAuthSecurityService,
    OAuthErrorHandlerService,
    OAuthRedirectManagerService,
    OAuthResponseHandlerService,
    FrontendValidationService,
    CorsHandlerService,
    RateLimiterService,
    AuditLoggingService,
    ErrorMonitoringService,
    InputValidationService,
  ],
  exports: [
    AuthService,
    TokenManagementService,
    TokenRotationService,
    TokenSecurityService,
    TokenErrorHandlerService,
  ],
})
export class AuthModule {}
