{"name": "orbitum", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "yarn kill-port && nest start", "start:dev": "yarn kill-port && nest start --watch", "start:debug": "yarn kill-port && nest start --debug --watch", "start:prod": "node dist/main", "start:api": "cross-env APP_ROLE=api node dist/main.api", "start:jobs": "cross-env APP_ROLE=jobs node dist/main.jobs", "start:api:dev": "cross-env APP_ROLE=api yarn kill-port && nest start --watch src/main.api.ts", "start:jobs:dev": "cross-env APP_ROLE=jobs yarn kill-port && nest start --watch src/main.jobs.ts", "dev": "nodemon", "dev:debug": "nodemon --config nodemon-debug.json", "dev:api": "cross-env APP_ROLE=api nodemon src/main.api.ts", "dev:jobs": "cross-env APP_ROLE=jobs nodemon src/main.jobs.ts", "kill-port": "npx kill-port 3001 || echo 'No process found on port 3001'", "kill-all-node": "taskkill /F /IM node.exe 2>nul || echo 'No node processes found'", "kill-nest": "taskkill /F /IM node.exe 2>nul || echo 'No nest processes found'", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "npx prisma generate", "prisma:migrate": "npx prisma migrate dev", "prisma:studio": "npx prisma studio", "test:scheduler": "node scripts/test-scheduler.js", "deploy": "flyctl deploy", "deploy:staging": "flyctl deploy --config fly.staging.toml"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@prisma/client": "^6.15.0", "@safe-global/api-kit": "^4.0.0", "@safe-global/protocol-kit": "^6.1.1", "@safe-global/types-kit": "^3.0.0", "@sendgrid/mail": "^8.1.5", "@types/passport-google-oauth20": "^2.0.16", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino-http": "^10.5.0", "prisma": "^6.15.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^6.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^24.3.0", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "cross-env": "^10.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "kill-port": "^2.0.1", "nodemon": "^3.1.10", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}